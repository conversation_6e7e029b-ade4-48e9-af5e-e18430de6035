/* <PERSON>'s Web Design - Main Stylesheet */
/* Color Palette: <PERSON> Blue (#0D1B2A), <PERSON><PERSON> (#4A5568), <PERSON> (#F7FAFC), Electric Blue (#3B82F6) */

@import url("https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@300;400;500;600&display=swap");

/* CSS Variables for futuristic theming */
:root {
  /* Core Background Colors */
  --bg-ultra-dark: #0d0d0d;
  --bg-charcoal: #1a1a1a;
  --bg-navy: #0f1419;
  --bg-glass: rgba(255, 255, 255, 0.05);

  /* Primary Accent Colors */
  --accent-cyan: #3bf0e4;
  --accent-blue: #3b82f6;
  --accent-emerald: #10b981;
  --accent-purple: #a855f7;
  --accent-gold: #facc15;

  /* Text Colors */
  --text-primary: #f5f5f5;
  --text-secondary: #a0aec0;
  --text-muted: #6b7280;

  /* Glow Effects */
  --glow-cyan: 0 0 20px rgba(59, 240, 228, 0.3);
  --glow-blue: 0 0 20px rgba(59, 130, 246, 0.3);
  --glow-emerald: 0 0 20px rgba(16, 185, 129, 0.3);
  --glow-purple: 0 0 20px rgba(168, 85, 247, 0.3);
  --glow-gold: 0 0 20px rgba(250, 204, 21, 0.3);

  /* Interactive States */
  --glow-intense-cyan: 0 0 40px rgba(59, 240, 228, 0.6);
  --glow-intense-blue: 0 0 40px rgba(59, 130, 246, 0.6);

  /* Gradients */
  --gradient-primary: linear-gradient(
    135deg,
    var(--bg-ultra-dark) 0%,
    var(--bg-charcoal) 100%
  );
  --gradient-accent: linear-gradient(
    135deg,
    var(--accent-cyan) 0%,
    var(--accent-blue) 100%
  );
  --gradient-glass: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );

  /* Borders */
  --border-glow: 1px solid rgba(59, 240, 228, 0.3);
  --border-glass: 1px solid rgba(255, 255, 255, 0.1);
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: "Inter", sans-serif;
  line-height: 1.6;
  color: var(--text-secondary);
  background: var(--bg-ultra-dark);
  overflow-x: hidden;
  position: relative;
}

body::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
      circle at 20% 80%,
      rgba(59, 240, 228, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(59, 130, 246, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 40%,
      rgba(168, 85, 247, 0.05) 0%,
      transparent 50%
    );
  pointer-events: none;
  z-index: -1;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Space Grotesk", sans-serif;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
  line-height: 1.2;
  text-shadow: 0 0 10px rgba(59, 240, 228, 0.2);
}

h1 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  background: var(--gradient-accent);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 600;
}

h3 {
  font-size: clamp(1.5rem, 3vw, 2.25rem);
  font-weight: 600;
}

h4 {
  font-size: clamp(1.25rem, 2.5vw, 1.75rem);
  font-weight: 600;
}

h5 {
  font-size: clamp(1.1rem, 2vw, 1.5rem);
  font-weight: 500;
}

h6 {
  font-size: clamp(1rem, 1.5vw, 1.25rem);
  font-weight: 500;
}

p {
  margin-bottom: 1rem;
  color: var(--text-secondary);
  font-weight: 400;
  line-height: 1.7;
}

.highlight {
  color: var(--accent-cyan);
  font-weight: 600;
  text-shadow: var(--glow-cyan);
}

.code-text {
  font-family: "JetBrains Mono", monospace;
  background: var(--bg-glass);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  border: var(--border-glass);
}

a {
  text-decoration: none;
  color: var(--accent-cyan);
  transition: all 0.3s ease;
  position: relative;
}

a:hover {
  color: var(--accent-blue);
  text-shadow: var(--glow-blue);
}

a.glow-link::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--gradient-accent);
  transition: width 0.3s ease;
  box-shadow: var(--glow-cyan);
}

a.glow-link:hover::after {
  width: 100%;
}

/* Container and Layout */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.section {
  padding: 5rem 0;
}

.section-title {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title h2 {
  margin-bottom: 1rem;
}

.section-title p {
  font-size: 1.1rem;
  max-width: 600px;
  margin: 0 auto;
}

/* Header and Navigation */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: rgba(13, 13, 13, 0.8);
  backdrop-filter: blur(20px) saturate(180%);
  border-bottom: var(--border-glow);
  z-index: 1000;
  transition: all 0.3s ease;
}

.header.scrolled {
  background: rgba(13, 13, 13, 0.95);
  box-shadow: 0 8px 32px rgba(59, 240, 228, 0.1);
  border-bottom: 1px solid rgba(59, 240, 228, 0.5);
}

.nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
}

.logo {
  font-family: "Space Grotesk", sans-serif;
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-primary);
  text-shadow: var(--glow-cyan);
  position: relative;
}

.logo span {
  color: var(--accent-cyan);
  text-shadow: var(--glow-intense-cyan);
}

.logo::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--gradient-accent);
  border-radius: 1px;
  opacity: 0.7;
}

.logo a {
  color: inherit;
  text-decoration: none;
}

.logo a:hover {
  color: inherit;
  text-shadow: inherit;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-link {
  color: var(--text-secondary);
  font-weight: 500;
  padding: 0.75rem 1.25rem;
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
  border: 1px solid transparent;
}

.nav-link:hover {
  color: var(--accent-cyan);
  background: var(--bg-glass);
  border: var(--border-glow);
  text-shadow: var(--glow-cyan);
  box-shadow: var(--glow-cyan);
}

.nav-link.active {
  color: var(--accent-cyan);
  background: var(--gradient-glass);
  border: var(--border-glow);
  text-shadow: var(--glow-cyan);
  box-shadow: var(--glow-cyan);
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 0.5rem;
}

.mobile-menu-toggle span {
  width: 25px;
  height: 3px;
  background: var(--pure-white);
  margin: 3px 0;
  transition: 0.3s;
  border-radius: 2px;
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
  font-family: "Space Grotesk", sans-serif;
}

.btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: var(--gradient-accent);
  color: var(--text-primary);
  box-shadow: var(--glow-cyan);
  border: 1px solid var(--accent-cyan);
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: var(--glow-intense-cyan);
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
}

.btn-secondary {
  background: var(--bg-glass);
  color: var(--accent-cyan);
  border: var(--border-glow);
  backdrop-filter: blur(10px);
}

.btn-secondary:hover {
  background: var(--gradient-glass);
  box-shadow: var(--glow-cyan);
  text-shadow: var(--glow-cyan);
}

.btn-outline {
  background: transparent;
  color: var(--text-primary);
  border: 2px solid var(--accent-cyan);
}

.btn-outline:hover {
  background: var(--accent-cyan);
  color: var(--bg-ultra-dark);
  box-shadow: var(--glow-intense-cyan);
  text-shadow: none;
}

/* Cards */
.card {
  background: var(--gradient-glass);
  backdrop-filter: blur(20px) saturate(180%);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transition: all 0.4s ease;
  border: var(--border-glass);
  position: relative;
  overflow: hidden;
}

.card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--gradient-accent);
  opacity: 0.5;
}

.card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(59, 240, 228, 0.2);
  border: var(--border-glow);
}

.card:hover::before {
  opacity: 1;
  box-shadow: var(--glow-cyan);
}

/* Grid System */
.grid {
  display: grid;
  gap: 2rem;
}

.grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}
.grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}
.grid-4 {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* Utilities */
.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}

.mb-1 {
  margin-bottom: 0.5rem;
}
.mb-2 {
  margin-bottom: 1rem;
}
.mb-3 {
  margin-bottom: 1.5rem;
}
.mb-4 {
  margin-bottom: 2rem;
}

.mt-1 {
  margin-top: 0.5rem;
}
.mt-2 {
  margin-top: 1rem;
}
.mt-3 {
  margin-top: 1.5rem;
}
.mt-4 {
  margin-top: 2rem;
}

.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }

  h1 {
    font-size: 2.5rem;
  }
  h2 {
    font-size: 2rem;
  }
  h3 {
    font-size: 1.5rem;
  }

  .container {
    padding: 0 1rem;
  }
  .section {
    padding: 3rem 0;
  }

  .nav-menu {
    position: fixed;
    top: 70px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 70px);
    background: var(--bg-ultra-dark);
    backdrop-filter: blur(20px);
    border-top: var(--border-glow);
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding-top: 2rem;
    transition: left 0.3s ease;
  }

  .nav-menu.active {
    left: 0;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 1rem;
  }
  .btn {
    padding: 0.8rem 1.5rem;
    font-size: 0.9rem;
  }
  .card {
    padding: 1.5rem;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .hero-buttons .btn {
    width: 100%;
    max-width: 280px;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .cta-buttons .btn {
    width: 100%;
    max-width: 280px;
  }

  .section-title {
    text-align: center;
    padding: 0 1rem;
  }

  .hero-content {
    padding: 0 1rem;
  }

  .page-hero-content {
    padding: 0 1rem;
    text-align: center;
  }
}

/* Hero Section */
.hero {
  background: var(--gradient-primary);
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse"><path d="M 40 0 L 0 0 0 40" fill="none" stroke="rgba(59,240,228,0.1)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>'),
    radial-gradient(
      circle at 30% 70%,
      rgba(59, 240, 228, 0.15) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 70% 30%,
      rgba(168, 85, 247, 0.1) 0%,
      transparent 50%
    );
  opacity: 0.8;
}

.hero::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 300px;
  height: 300px;
  background: radial-gradient(
    circle,
    rgba(59, 240, 228, 0.1) 0%,
    transparent 70%
  );
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulse 4s ease-in-out infinite;
}

@keyframes pulse {
  0%,
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.5;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.8;
  }
}

.hero-content {
  text-align: center;
  color: var(--text-primary);
  position: relative;
  z-index: 2;
  max-width: 900px;
  margin: 0 auto;
}

.hero-content h1 {
  color: var(--text-primary);
  font-size: clamp(3rem, 6vw, 5rem);
  margin-bottom: 1.5rem;
  font-weight: 700;
  background: var(--gradient-accent);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px rgba(59, 240, 228, 0.3);
  animation: textGlow 3s ease-in-out infinite alternate;
}

@keyframes textGlow {
  from {
    text-shadow: 0 0 20px rgba(59, 240, 228, 0.3);
  }
  to {
    text-shadow: 0 0 40px rgba(59, 240, 228, 0.6);
  }
}

.hero-subtitle {
  font-size: clamp(1.1rem, 2.5vw, 1.4rem);
  margin-bottom: 2.5rem;
  color: var(--text-secondary);
  line-height: 1.7;
  font-weight: 400;
}

.hero-buttons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Service Cards */
.service-icon {
  color: var(--accent-cyan);
  margin-bottom: 1.5rem;
  text-align: center;
  filter: drop-shadow(var(--glow-cyan));
  transition: all 0.3s ease;
}

.service-icon svg {
  width: 60px;
  height: 60px;
  transition: all 0.3s ease;
}

.card:hover .service-icon {
  color: var(--accent-blue);
  filter: drop-shadow(var(--glow-intense-blue));
  transform: scale(1.1);
}

.card:hover .service-icon svg {
  animation: iconFloat 2s ease-in-out infinite;
}

@keyframes iconFloat {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

.service-link {
  display: inline-block;
  margin-top: 1rem;
  color: var(--accent-cyan);
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  text-shadow: var(--glow-cyan);
}

.service-link::after {
  content: "→";
  margin-left: 0.5rem;
  transition: all 0.3s ease;
}

.service-link:hover {
  color: var(--accent-blue);
  text-shadow: var(--glow-intense-blue);
  transform: translateX(5px);
}

.service-link:hover::after {
  transform: translateX(5px);
  text-shadow: var(--glow-intense-blue);
}

/* Background Gradient Section */
.bg-gradient {
  background: var(--gradient-primary);
  color: var(--text-primary);
  position: relative;
  overflow: hidden;
}

.bg-gradient::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 20% 20%,
      rgba(59, 240, 228, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 80%,
      rgba(168, 85, 247, 0.1) 0%,
      transparent 50%
    );
  pointer-events: none;
}

/* Feature List */
.feature-list {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.feature-icon {
  background: var(--gradient-accent);
  color: var(--text-primary);
  width: 35px;
  height: 35px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
  box-shadow: var(--glow-cyan);
  border: 1px solid var(--accent-cyan);
  transition: all 0.3s ease;
}

.feature-item:hover .feature-icon {
  transform: scale(1.1);
  box-shadow: var(--glow-intense-cyan);
}

/* Stats */
.stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
}

.stat-item {
  text-align: center;
  padding: 2rem;
  background: var(--gradient-glass);
  border-radius: 16px;
  backdrop-filter: blur(20px) saturate(180%);
  border: var(--border-glass);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-accent);
  opacity: 0.7;
}

.stat-item:hover {
  transform: translateY(-5px);
  border: var(--border-glow);
  box-shadow: var(--glow-cyan);
}

.stat-number {
  font-size: clamp(2.5rem, 4vw, 3.5rem);
  font-weight: 800;
  color: var(--accent-cyan);
  margin-bottom: 0.5rem;
  text-shadow: var(--glow-cyan);
  font-family: "Space Grotesk", sans-serif;
}

.stat-label {
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 1rem;
}

/* Testimonials */
.testimonial-card {
  text-align: center;
}

.stars {
  color: #ffd700;
  font-size: 1.2rem;
  margin-bottom: 1rem;
}

.testimonial-content p {
  font-style: italic;
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
}

.testimonial-author strong {
  color: var(--primary-dark);
  display: block;
  margin-bottom: 0.25rem;
}

.testimonial-author span {
  color: var(--text-light);
  font-size: 0.9rem;
}

/* CTA Section */
.cta-section {
  background: var(--soft-white);
  border-top: 1px solid var(--border-light);
  border-bottom: 1px solid var(--border-light);
}

.cta-content h2 {
  margin-bottom: 1rem;
}

.cta-buttons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 2rem;
}

/* Footer */
.footer {
  background: var(--bg-ultra-dark);
  color: var(--text-primary);
  padding: 3rem 0 1rem;
  position: relative;
  border-top: var(--border-glow);
}

.footer::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 10% 90%,
      rgba(59, 240, 228, 0.05) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 90% 10%,
      rgba(168, 85, 247, 0.05) 0%,
      transparent 50%
    );
  pointer-events: none;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h4 {
  color: var(--accent-cyan);
  margin-bottom: 1rem;
  font-family: "Space Grotesk", sans-serif;
  text-shadow: var(--glow-cyan);
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 0.75rem;
}

.footer-section ul li a {
  color: var(--text-secondary);
  transition: all 0.3s ease;
  position: relative;
  padding-left: 1rem;
}

.footer-section ul li a::before {
  content: "▸";
  position: absolute;
  left: 0;
  color: var(--accent-cyan);
  opacity: 0;
  transition: all 0.3s ease;
}

.footer-section ul li a:hover {
  color: var(--accent-cyan);
  text-shadow: var(--glow-cyan);
  transform: translateX(5px);
}

.footer-section ul li a:hover::before {
  opacity: 1;
}

.social-links {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.social-links a {
  font-size: 1.5rem;
  transition: transform 0.3s ease;
}

.social-links a:hover {
  transform: scale(1.2);
}

.contact-info p {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0.5rem;
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.7);
}

/* Page Hero */
.page-hero {
  background: var(--gradient-primary);
  padding: 8rem 0 5rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.page-hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 30% 70%,
      rgba(59, 240, 228, 0.15) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 70% 30%,
      rgba(168, 85, 247, 0.1) 0%,
      transparent 50%
    );
  opacity: 0.8;
}

.page-hero-content {
  position: relative;
  z-index: 2;
}

.page-hero-content h1 {
  color: var(--text-primary);
  font-size: clamp(2.5rem, 5vw, 4rem);
  margin-bottom: 1.5rem;
  font-weight: 700;
  background: var(--gradient-accent);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px rgba(59, 240, 228, 0.3);
}

.page-hero-content p {
  font-size: 1.3rem;
  color: var(--text-secondary);
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.6;
  font-weight: 400;
}

/* Service Detail Sections */
.service-detail {
  padding: 5rem 0;
}

.service-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.service-content.reverse {
  direction: rtl;
}

.service-content.reverse > * {
  direction: ltr;
}

.service-icon-large {
  color: var(--accent-cyan);
  margin-bottom: 2rem;
  filter: drop-shadow(var(--glow-cyan));
  transition: all 0.3s ease;
}

.service-icon-large:hover {
  color: var(--accent-blue);
  filter: drop-shadow(var(--glow-intense-blue));
  transform: scale(1.05);
}

.service-description {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  color: var(--text-secondary);
  line-height: 1.7;
}

.service-features h3,
.service-process h3,
.service-benefits h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 1.3rem;
  font-weight: 600;
}

.service-features ul {
  list-style: none;
  padding: 0;
}

.service-features li {
  padding: 0.5rem 0;
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 1.05rem;
}

/* Process Steps */
.process-steps {
  display: grid;
  gap: 1.5rem;
  margin-top: 1rem;
}

.process-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.step-number {
  background: linear-gradient(135deg, var(--accent-cyan), var(--accent-blue));
  color: var(--text-primary);
  width: 45px;
  height: 45px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
  box-shadow: var(--glow-cyan);
  transition: all 0.3s ease;
}

.step-number:hover {
  transform: scale(1.1);
  box-shadow: var(--glow-intense-cyan);
}

.step-content h4 {
  margin-bottom: 0.5rem;
  color: var(--text-primary);
  font-weight: 600;
}

.step-content p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 1.05rem;
}

/* Benefits Grid */
.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.benefit-item {
  padding: 1rem;
  background: var(--pure-white);
  border-radius: 8px;
  border: 1px solid var(--border-light);
}

.benefit-item h4 {
  color: var(--primary-dark);
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.benefit-item p {
  color: var(--text-light);
  font-size: 0.9rem;
  margin: 0;
}

/* SEO Metrics */
.seo-metrics {
  margin-top: 2rem;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  margin-top: 1rem;
}

.metric-item {
  text-align: center;
  padding: 1.5rem;
  background: var(--soft-white);
  border-radius: 12px;
  border: 1px solid var(--border-light);
}

.metric-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--accent-blue);
  margin-bottom: 0.5rem;
}

.metric-label {
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 0.9rem;
}

/* Integration Types */
.integration-types {
  margin-top: 2rem;
}

.integration-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 0.8rem;
  margin-top: 1.5rem;
}

.integration-item {
  padding: 0.8rem 1.2rem;
  background: transparent;
  color: var(--text-light);
  text-align: left;
  border-radius: 6px;
  font-weight: 400;
  font-size: 0.9rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.2s ease;
}

.integration-item:hover {
  color: var(--accent-cyan);
  border-color: var(--accent-cyan);
}

/* Background Variants */
.bg-gradient-alt {
  background: linear-gradient(
    135deg,
    var(--bg-charcoal) 0%,
    var(--bg-navy) 100%
  );
}

/* Service Mockups */
.service-mockup {
  max-width: 480px;
  margin: 0 auto;
  width: 100%;
}

/* Browser Mockup */
.mockup-browser {
  background: var(--bg-charcoal);
  border-radius: 16px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
  overflow: hidden;
  border: var(--border-glass);
}

.browser-header {
  background: var(--bg-ultra-dark);
  padding: 1rem;
  border-bottom: var(--border-glass);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.browser-buttons {
  display: flex;
  gap: 0.5rem;
}

.browser-buttons span {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #ff5f57;
}

.browser-buttons span:nth-child(2) {
  background: #ffbd2e;
}

.browser-buttons span:nth-child(3) {
  background: #28ca42;
}

.browser-url {
  flex: 1;
  height: 28px;
  background: var(--gradient-glass);
  border-radius: 14px;
  border: var(--border-glass);
  display: flex;
  align-items: center;
  padding: 0 1rem;
  color: var(--text-secondary);
  font-size: 0.85rem;
}

.browser-content {
  padding: 0;
  background: var(--bg-ultra-dark);
}

.mockup-website {
  background: var(--bg-ultra-dark);
  padding: 2rem;
  min-height: 300px;
}

.mockup-header {
  height: 60px;
  background: var(--gradient-accent);
  border-radius: 8px;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1.1rem;
  box-shadow: var(--glow-cyan);
}

.mockup-header::after {
  content: "Your Brand";
}

.mockup-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.mockup-section {
  background: var(--gradient-glass);
  border-radius: 8px;
  padding: 1.5rem;
  border: var(--border-glass);
}

.mockup-section-title {
  height: 20px;
  background: var(--accent-cyan);
  border-radius: 4px;
  margin-bottom: 1rem;
  opacity: 0.8;
}

.mockup-text-line {
  height: 8px;
  background: var(--text-muted);
  border-radius: 4px;
  margin-bottom: 0.5rem;
  opacity: 0.6;
}

.mockup-text-line.short {
  width: 70%;
}

.mockup-text-line.medium {
  width: 85%;
}

/* UX Mockup */
.ux-mockup {
  background: var(--bg-charcoal);
  border-radius: 16px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
  padding: 2rem;
  border: var(--border-glass);
}

.ux-wireframe {
  background: var(--bg-ultra-dark);
  border-radius: 12px;
  padding: 1.5rem;
  border: var(--border-glass);
}

.wireframe-title {
  color: var(--accent-cyan);
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  text-align: center;
}

.wireframe-title::after {
  content: "User Interface Design";
}

.wireframe-header {
  height: 40px;
  background: var(--gradient-accent);
  border-radius: 6px;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
  font-size: 0.8rem;
  font-weight: 600;
  box-shadow: var(--glow-cyan);
}

.wireframe-header::after {
  content: "Navigation";
}

.wireframe-content {
  display: flex;
  gap: 1.5rem;
}

.wireframe-sidebar {
  width: 100px;
  height: 140px;
  background: var(--gradient-glass);
  border-radius: 6px;
  border: var(--border-glass);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.sidebar-item {
  width: 60px;
  height: 8px;
  background: var(--accent-purple);
  border-radius: 4px;
  opacity: 0.7;
}

.wireframe-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.wireframe-card {
  height: 42px;
  background: var(--gradient-glass);
  border-radius: 6px;
  border: var(--border-glass);
  display: flex;
  align-items: center;
  padding: 0 1rem;
  position: relative;
}

.wireframe-card::before {
  content: "";
  width: 30px;
  height: 6px;
  background: var(--accent-emerald);
  border-radius: 3px;
  opacity: 0.8;
}

.wireframe-card::after {
  content: "";
  position: absolute;
  right: 1rem;
  width: 40px;
  height: 6px;
  background: var(--text-muted);
  border-radius: 3px;
  opacity: 0.5;
}

/* SEO Mockup */
.seo-mockup {
  background: var(--bg-charcoal);
  border-radius: 16px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
  padding: 2rem;
  border: var(--border-glass);
}

.seo-chart {
  background: var(--bg-ultra-dark);
  border-radius: 12px;
  padding: 2rem;
  border: var(--border-glass);
}

.chart-header {
  font-weight: 600;
  color: var(--accent-cyan);
  margin-bottom: 2rem;
  text-align: center;
  font-size: 1rem;
}

.chart-header::after {
  content: "SEO Performance Analytics";
}

.chart-content {
  height: 140px;
  display: flex;
  align-items: end;
  justify-content: center;
  position: relative;
}

.chart-bars {
  display: flex;
  gap: 0.75rem;
  align-items: end;
  height: 120px;
}

.chart-bar {
  width: 24px;
  background: linear-gradient(180deg, var(--accent-cyan), var(--accent-blue));
  border-radius: 4px 4px 0 0;
  animation: growUp 2s ease-out;
  box-shadow: var(--glow-cyan);
  position: relative;
}

.chart-bar:nth-child(1) {
  height: 40%;
  animation-delay: 0.2s;
}
.chart-bar:nth-child(2) {
  height: 65%;
  animation-delay: 0.4s;
}
.chart-bar:nth-child(3) {
  height: 85%;
  animation-delay: 0.6s;
}
.chart-bar:nth-child(4) {
  height: 95%;
  animation-delay: 0.8s;
}
.chart-bar:nth-child(5) {
  height: 100%;
  animation-delay: 1s;
}

.chart-labels {
  display: flex;
  justify-content: center;
  gap: 0.75rem;
  margin-top: 1rem;
}

.chart-label {
  width: 24px;
  text-align: center;
  font-size: 0.7rem;
  color: var(--text-muted);
}

.seo-metrics {
  display: flex;
  justify-content: space-around;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: var(--border-glass);
}

.seo-metric {
  text-align: center;
}

.seo-metric-value {
  color: var(--accent-emerald);
  font-weight: 700;
  font-size: 1.2rem;
  margin-bottom: 0.25rem;
}

.seo-metric-label {
  color: var(--text-secondary);
  font-size: 0.8rem;
}

@keyframes growUp {
  from {
    height: 0;
    opacity: 0;
  }
  to {
    height: var(--final-height, 100%);
    opacity: 1;
  }
}

/* Backend Mockup */
.backend-mockup {
  background: var(--bg-ultra-dark);
  border-radius: 16px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
  overflow: hidden;
  border: var(--border-glass);
}

.code-editor {
  color: var(--text-primary);
  font-family: "JetBrains Mono", "Courier New", monospace;
  font-size: 0.85rem;
}

.editor-header {
  background: var(--bg-charcoal);
  padding: 0.75rem 1rem;
  border-bottom: var(--border-glass);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.editor-tabs {
  display: flex;
  gap: 0.5rem;
}

.tab {
  padding: 0.5rem 1rem;
  border-radius: 6px 6px 0 0;
  font-size: 0.8rem;
  background: var(--bg-ultra-dark);
  border: var(--border-glass);
  border-bottom: none;
  color: var(--text-secondary);
  transition: all 0.3s ease;
  position: relative;
}

.tab.active {
  background: var(--bg-ultra-dark);
  color: var(--accent-cyan);
  border-color: var(--accent-cyan);
  box-shadow: 0 0 10px rgba(59, 240, 228, 0.3);
}

.tab.active::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--accent-cyan);
  box-shadow: var(--glow-cyan);
}

.editor-status {
  color: var(--accent-emerald);
  font-size: 0.75rem;
  font-weight: 600;
}

.editor-status::after {
  content: "● Connected";
}

.editor-content {
  padding: 1.5rem 2rem 2.5rem 2rem;
  line-height: 1.6;
  background: var(--bg-ultra-dark);
  font-size: 0.8rem;
  height: 300px;
  overflow: hidden;
}

.code-line {
  display: flex;
  align-items: center;
  white-space: nowrap;
  height: 1.8rem;
  padding: 0;
  transition: background-color 0.2s ease;
  margin: 0;
}

.code-line:hover {
  background: rgba(59, 240, 228, 0.05);
}

.line-number {
  color: var(--text-muted);
  width: 35px;
  font-size: 0.75rem;
  margin-right: 1.5rem;
  flex-shrink: 0;
  text-align: right;
  opacity: 0.6;
  user-select: none;
}

.code-text {
  flex: 1;
  font-family: "JetBrains Mono", "Courier New", monospace;
  font-size: 0.8rem;
  line-height: 1.6;
  overflow: hidden;
  text-overflow: ellipsis;
}

.keyword {
  color: #c792ea;
  font-weight: 600;
}
.function {
  color: #82aaff;
  font-weight: 500;
}
.string {
  color: #c3e88d;
}
.comment {
  color: #546e7a;
  font-style: italic;
  opacity: 0.8;
}
.variable {
  color: #ffcb6b;
}
.operator {
  color: #89ddff;
}
.punctuation {
  color: #89ddff;
}

/* Pricing Section */
.pricing-section {
  background: var(--soft-white);
}

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.pricing-card {
  position: relative;
  text-align: center;
  padding: 2.5rem 2rem;
  background: var(--pure-white);
  border: 2px solid var(--border-light);
  transition: all 0.3s ease;
}

.pricing-card:hover {
  border-color: var(--accent-blue);
  transform: translateY(-5px);
}

.pricing-card.featured {
  border-color: var(--accent-blue);
  transform: scale(1.05);
}

.pricing-card.featured:hover {
  transform: scale(1.05) translateY(-5px);
}

.pricing-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--accent-blue);
  color: var(--pure-white);
  padding: 0.5rem 1.5rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.pricing-header h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: var(--primary-dark);
}

.price {
  font-size: 3rem;
  font-weight: 800;
  color: var(--accent-blue);
  margin-bottom: 0.5rem;
}

.pricing-header p {
  color: var(--text-light);
  margin-bottom: 2rem;
}

.pricing-features {
  margin-bottom: 2rem;
}

.pricing-features ul {
  list-style: none;
  padding: 0;
  text-align: left;
}

.pricing-features li {
  padding: 0.75rem 0;
  color: var(--text-light);
  border-bottom: 1px solid var(--border-light);
  font-weight: 500;
}

.pricing-features li:last-child {
  border-bottom: none;
}

/* Responsive Design for Services */
@media (max-width: 768px) {
  .page-hero-content h1 {
    font-size: 2.5rem;
  }

  .service-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .service-content.reverse {
    direction: ltr;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .benefits-grid {
    grid-template-columns: 1fr;
  }

  .integration-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .pricing-grid {
    grid-template-columns: 1fr;
  }

  .pricing-card.featured {
    transform: none;
  }

  .pricing-card.featured:hover {
    transform: translateY(-5px);
  }
}

@media (max-width: 480px) {
  .page-hero {
    padding: 6rem 0 3rem;
  }

  .service-detail {
    padding: 3rem 0;
  }

  .service-mockup {
    max-width: 300px;
  }

  .integration-grid {
    grid-template-columns: 1fr;
  }
}

/* About Page Styles */
.about-content {
  display: grid;
  grid-template-columns: 1.8fr 1.2fr;
  gap: 5rem;
  align-items: center;
  margin-bottom: 4rem;
}

.about-text .lead {
  font-size: 1.4rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2rem;
  line-height: 1.5;
}

.about-text p {
  margin-bottom: 1.8rem;
  line-height: 1.8;
  color: var(--text-secondary);
  font-size: 1.1rem;
}

/* About Visual */
.about-visual {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  position: relative;
}

.visual-element {
  position: relative;
  width: 280px;
  height: 280px;
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.element-circle {
  position: absolute;
  top: 0;
  left: 0;
  width: 140px;
  height: 140px;
  background: linear-gradient(135deg, var(--accent-cyan), var(--accent-blue));
  border-radius: 50%;
  opacity: 0.9;
  box-shadow: var(--glow-cyan);
  animation: pulse 4s ease-in-out infinite;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.element-square {
  position: absolute;
  top: 60px;
  right: 0;
  width: 100px;
  height: 100px;
  background: linear-gradient(
    135deg,
    var(--accent-purple),
    var(--accent-emerald)
  );
  border-radius: 16px;
  opacity: 0.8;
  box-shadow: var(--glow-purple);
  animation: rotate 8s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.element-triangle {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 50px solid transparent;
  border-right: 50px solid transparent;
  border-bottom: 80px solid var(--accent-gold);
  opacity: 0.7;
  filter: drop-shadow(var(--glow-gold));
  animation: bounce 3s ease-in-out infinite;
}

@keyframes bounce {
  0%,
  100% {
    transform: translateX(-50%) translateY(0px);
  }
  50% {
    transform: translateX(-50%) translateY(-10px);
  }
}

/* Values Grid */
.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2.5rem;
  margin-top: 4rem;
}

.value-card {
  text-align: center;
  padding: 3rem 2.5rem;
  position: relative;
  overflow: hidden;
}

.value-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-accent);
  opacity: 0.8;
}

.value-icon {
  font-size: 3.5rem;
  margin-bottom: 2rem;
  display: block;
  filter: drop-shadow(0 4px 8px rgba(59, 240, 228, 0.3));
}

.value-card h3 {
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  font-size: 1.4rem;
  font-weight: 700;
}

.value-card p {
  color: var(--text-secondary);
  line-height: 1.7;
  font-size: 1.05rem;
}

/* Team Grid */
.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 2.5rem;
  margin-top: 4rem;
}

.team-member {
  padding: 3rem 2.5rem;
  position: relative;
  overflow: hidden;
}

.team-member::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-accent);
  opacity: 0.8;
}

.member-photo {
  text-align: center;
  margin-bottom: 2rem;
}

.photo-placeholder {
  width: 140px;
  height: 140px;
  background: linear-gradient(135deg, var(--accent-cyan), var(--accent-blue));
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
  font-size: 2.5rem;
  font-weight: bold;
  box-shadow: var(--glow-cyan);
  transition: all 0.3s ease;
}

.team-member:hover .photo-placeholder {
  transform: scale(1.05);
  box-shadow: var(--glow-intense-cyan);
}

.member-info h3 {
  text-align: center;
  margin-bottom: 0.75rem;
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 700;
}

.member-role {
  text-align: center;
  color: var(--accent-cyan);
  font-weight: 600;
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
  text-shadow: var(--glow-cyan);
}

.member-bio {
  color: var(--text-secondary);
  line-height: 1.7;
  margin-bottom: 2rem;
  font-size: 1.05rem;
  text-align: center;
}

.member-skills {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  justify-content: center;
}

.skill-tag {
  background: var(--gradient-glass);
  color: var(--text-primary);
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 500;
  border: var(--border-glass);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.skill-tag:hover {
  background: var(--gradient-accent);
  color: var(--bg-ultra-dark);
  transform: translateY(-2px);
  box-shadow: var(--glow-cyan);
}

/* Process Timeline */
.process-timeline {
  max-width: 900px;
  margin: 0 auto;
  position: relative;
  padding: 2rem 0;
}

.process-timeline::before {
  content: "";
  position: absolute;
  left: 35px;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(
    180deg,
    var(--accent-cyan),
    var(--accent-blue),
    var(--accent-purple)
  );
  border-radius: 2px;
  box-shadow: var(--glow-cyan);
}

.timeline-item {
  position: relative;
  padding-left: 100px;
  margin-bottom: 4rem;
}

.timeline-marker {
  position: absolute;
  left: 0;
  top: 0;
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, var(--accent-cyan), var(--accent-blue));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
  font-size: 1.8rem;
  font-weight: bold;
  border: 4px solid var(--bg-ultra-dark);
  box-shadow: var(--glow-cyan);
  transition: all 0.3s ease;
}

.timeline-item:hover .timeline-marker {
  transform: scale(1.1);
  box-shadow: var(--glow-intense-cyan);
}

.timeline-content h3 {
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  font-weight: 700;
}

.timeline-content p {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
  line-height: 1.7;
}

.timeline-content ul {
  list-style: none;
  padding: 0;
}

.timeline-content li {
  color: var(--text-secondary);
  padding: 0.5rem 0;
  position: relative;
  padding-left: 2rem;
  font-size: 1.05rem;
  transition: all 0.3s ease;
}

.timeline-content li:hover {
  color: var(--accent-cyan);
  transform: translateX(5px);
}

.timeline-content li::before {
  content: "▶";
  position: absolute;
  left: 0;
  color: var(--accent-cyan);
  font-size: 0.8rem;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: 2.5rem;
  margin-top: 4rem;
  align-items: stretch;
}

/* Force 4 columns on large screens so all cards stay on one row */
@media (min-width: 1100px) {
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.stat-card {
  text-align: center;
  padding: 3rem 2.5rem;
  background: var(--gradient-glass);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border: var(--border-glass);
  backdrop-filter: blur(20px) saturate(180%);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.stat-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-accent);
  opacity: 0.8;
}

.stat-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(59, 240, 228, 0.2);
  border: var(--border-glow);
}

.stat-card .stat-number {
  font-size: 4rem;
  font-weight: 800;
  color: var(--accent-cyan);
  margin-bottom: 1rem;
  text-shadow: var(--glow-cyan);
  font-family: "Space Grotesk", sans-serif;
}

.stat-card .stat-label {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
}

.stat-card p {
  color: var(--text-secondary);
  font-size: 1.05rem;
  margin: 0;
  line-height: 1.6;
}

/* Responsive Design for About Page */
@media (max-width: 768px) {
  .about-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .about-visual {
    height: 300px;
  }

  .values-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .team-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .process-timeline::before {
    left: 20px;
  }

  .timeline-item {
    padding-left: 70px;
    margin-bottom: 3rem;
  }

  .timeline-marker {
    width: 50px;
    height: 50px;
    font-size: 1.4rem;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  .stat-card {
    padding: 2.5rem 2rem;
  }

  .stat-card .stat-number {
    font-size: 3rem;
  }
}

@media (max-width: 480px) {
  .visual-element {
    width: 150px;
    height: 150px;
  }

  .element-circle {
    width: 90px;
    height: 90px;
  }

  .element-square {
    width: 60px;
    height: 60px;
    top: 30px;
  }

  .element-triangle {
    border-left-width: 30px;
    border-right-width: 30px;
    border-bottom-width: 45px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .timeline-item {
    padding-left: 50px;
  }

  .timeline-marker {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }
}

/* Portfolio Page Styles */
.portfolio-filter {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.portfolio-filter-btn {
  padding: 0.75rem 1.5rem;
  border: 2px solid var(--border-light);
  background: var(--pure-white);
  color: var(--secondary-gray);
  border-radius: 25px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.portfolio-filter-btn:hover,
.portfolio-filter-btn.active {
  background: var(--accent-blue);
  color: var(--pure-white);
  border-color: var(--accent-blue);
}

/* Portfolio Grid */
.portfolio-section {
  padding-top: 2rem;
}

.portfolio-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 3rem;
}

.portfolio-item {
  background: var(--pure-white);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px var(--shadow-light);
  transition: all 0.3s ease;
  opacity: 1;
  transform: scale(1);
}

.portfolio-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 12px 40px var(--shadow-medium);
}

/* Portfolio Image */
.portfolio-image {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.project-mockup {
  width: 100%;
  height: 100%;
  padding: 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.mockup-screen {
  width: 100%;
  height: 100%;
  background: var(--pure-white);
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.mockup-header {
  height: 30px;
  background: var(--border-light);
  position: relative;
}

.mockup-header::before {
  content: "";
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ff5f57;
  box-shadow: 12px 0 0 #ffbd2e, 24px 0 0 #28ca42;
}

.mockup-content {
  padding: 1rem;
  height: calc(100% - 30px);
}

/* Different mockup styles */
.ecommerce-header {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}
.business-header {
  background: linear-gradient(90deg, #f093fb 0%, #f5576c 100%);
}
.startup-header {
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
}
.nonprofit-header {
  background: linear-gradient(90deg, #43e97b 0%, #38f9d7 100%);
}
.restaurant-header {
  background: linear-gradient(90deg, #fa709a 0%, #fee140 100%);
}
.tech-header {
  background: linear-gradient(90deg, #a8edea 0%, #fed6e3 100%);
}

/* Product Grid for E-commerce */
.product-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
  height: 100%;
}

.product-item {
  background: var(--soft-white);
  border-radius: 4px;
  border: 1px solid var(--border-light);
}

/* Business Layout */
.business-layout {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.hero-section {
  height: 40%;
  background: var(--accent-blue);
  border-radius: 4px;
  opacity: 0.8;
}

.services-section {
  display: flex;
  gap: 0.5rem;
  height: 60%;
}

.service-card {
  flex: 1;
  background: var(--soft-white);
  border-radius: 4px;
  border: 1px solid var(--border-light);
}

/* Startup Layout */
.startup-layout {
  height: 100%;
  display: flex;
  gap: 0.5rem;
}

.app-showcase {
  width: 60%;
  background: var(--accent-blue);
  border-radius: 4px;
  opacity: 0.8;
}

.feature-list {
  width: 40%;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.feature-item {
  flex: 1;
  background: var(--soft-white);
  border-radius: 4px;
  border: 1px solid var(--border-light);
}

/* Non-profit Layout */
.nonprofit-layout {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.cause-banner {
  height: 50%;
  background: linear-gradient(45deg, #43e97b, #38f9d7);
  border-radius: 4px;
  opacity: 0.8;
}

.donation-section {
  height: 50%;
  display: flex;
  gap: 0.5rem;
}

.donation-card {
  width: 70%;
  background: var(--soft-white);
  border-radius: 4px;
  border: 1px solid var(--border-light);
}

.progress-bar {
  width: 30%;
  background: var(--accent-blue);
  border-radius: 4px;
  opacity: 0.6;
}

/* Restaurant Layout */
.restaurant-layout {
  height: 100%;
  display: flex;
  gap: 0.5rem;
}

.menu-showcase {
  width: 65%;
  background: linear-gradient(45deg, #fa709a, #fee140);
  border-radius: 4px;
  opacity: 0.8;
}

.reservation-form {
  width: 35%;
  background: var(--soft-white);
  border-radius: 4px;
  border: 1px solid var(--border-light);
}

/* Tech Layout */
.tech-layout {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.product-showcase {
  height: 60%;
  background: linear-gradient(45deg, #a8edea, #fed6e3);
  border-radius: 4px;
  opacity: 0.8;
}

.tech-specs {
  height: 40%;
  display: flex;
  gap: 0.5rem;
}

.spec-item {
  flex: 1;
  background: var(--soft-white);
  border-radius: 4px;
  border: 1px solid var(--border-light);
}

/* Portfolio Overlay */
.portfolio-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(13, 27, 42, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
}

.portfolio-item:hover .portfolio-overlay {
  opacity: 1;
}

.overlay-content {
  text-align: center;
  color: var(--pure-white);
  padding: 2rem;
}

.overlay-content h3 {
  color: var(--pure-white);
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
}

.overlay-content p {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 1rem;
}

.project-tags {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.project-tags span {
  background: var(--accent-blue);
  color: var(--pure-white);
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Portfolio Info */
.portfolio-info {
  padding: 2rem;
}

.portfolio-info h3 {
  margin-bottom: 1rem;
  color: var(--primary-dark);
}

.portfolio-info p {
  color: var(--text-light);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

/* Project Results */
.project-results {
  display: flex;
  gap: 2rem;
  justify-content: center;
}

.result-item {
  text-align: center;
}

.result-number {
  display: block;
  font-size: 2rem;
  font-weight: 800;
  color: var(--accent-blue);
  margin-bottom: 0.25rem;
}

.result-label {
  font-size: 0.9rem;
  color: var(--text-light);
  font-weight: 500;
}

/* Process Steps Grid */
.process-steps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.process-step-card {
  text-align: center;
  padding: 2.5rem 2rem;
}

.step-icon {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  display: block;
}

.process-step-card h3 {
  margin-bottom: 1rem;
  color: var(--primary-dark);
}

.process-step-card p {
  color: var(--text-light);
  line-height: 1.6;
}

/* Testimonials Slider */
.testimonials-slider {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.testimonial-slide {
  padding: 2rem;
}

.testimonial-slide .stars {
  color: #ffd700;
  font-size: 1.5rem;
  margin-bottom: 2rem;
}

.testimonial-slide blockquote {
  font-size: 1.3rem;
  font-style: italic;
  color: var(--text-light);
  margin-bottom: 2rem;
  line-height: 1.6;
  quotes: "" " " "" "'" "'";
}

.testimonial-slide blockquote::before {
  content: open-quote;
  font-size: 2rem;
  color: var(--accent-blue);
}

.testimonial-slide blockquote::after {
  content: close-quote;
  font-size: 2rem;
  color: var(--accent-blue);
}

.testimonial-author strong {
  color: var(--primary-dark);
  display: block;
  margin-bottom: 0.25rem;
  font-size: 1.1rem;
}

.testimonial-author span {
  color: var(--text-light);
  font-size: 0.9rem;
}

/* Responsive Design for Portfolio */
@media (max-width: 768px) {
  .portfolio-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .portfolio-filter {
    gap: 0.5rem;
  }

  .portfolio-filter-btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  .project-results {
    gap: 1rem;
  }

  .result-number {
    font-size: 1.5rem;
  }

  .process-steps-grid {
    grid-template-columns: 1fr;
  }

  .testimonial-slide blockquote {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .portfolio-grid {
    grid-template-columns: 1fr;
  }

  .portfolio-item {
    margin: 0 1rem;
  }

  .portfolio-info {
    padding: 1.5rem;
  }

  .project-results {
    flex-direction: column;
    gap: 1rem;
  }

  .overlay-content {
    padding: 1rem;
  }

  .overlay-content h3 {
    font-size: 1.2rem;
  }
}

/* Contact Page Styles */
.contact-section {
  padding: 5rem 0;
  background: linear-gradient(135deg, var(--soft-white) 0%, #f8fafc 100%);
  position: relative;
}

.contact-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 20% 80%,
      rgba(59, 130, 246, 0.03) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(16, 185, 129, 0.03) 0%,
      transparent 50%
    );
  pointer-events: none;
}

.contact-content {
  display: grid;
  grid-template-columns: 1.6fr 1.4fr;
  gap: 4rem;
  align-items: start;
  max-width: 1300px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

/* Contact Form */
.contact-form-container h2 {
  margin-bottom: 1rem;
  color: var(--primary-dark);
  font-size: 2.2rem;
  font-weight: 700;
}

.contact-form-container p {
  color: var(--secondary-gray);
  margin-bottom: 2.5rem;
  font-size: 1.1rem;
  line-height: 1.6;
  font-weight: 500;
}

.contact-form {
  background: var(--pure-white);
  padding: 2.5rem;
  border-radius: 16px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  position: relative;
  overflow: hidden;
}

.contact-form::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-blue), var(--primary-dark));
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.form-group {
  margin-bottom: 2rem;
  position: relative;
}

.form-group label {
  display: block;
  margin-bottom: 0.75rem;
  color: var(--primary-dark);
  font-weight: 600;
  font-size: 0.95rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 1.2rem 1.5rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: #fafbfc;
  color: var(--secondary-gray);
  font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--accent-blue);
  background: var(--pure-white);
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.form-group input:hover,
.form-group select:hover,
.form-group textarea:hover {
  border-color: #cbd5e1;
  background: var(--pure-white);
}

.form-group textarea {
  resize: vertical;
  min-height: 140px;
  line-height: 1.6;
}

/* Checkbox Group */
.checkbox-group {
  margin: 2.5rem 0;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  cursor: pointer;
  color: var(--text-light);
  line-height: 1.5;
  font-size: 0.95rem;
  transition: color 0.3s ease;
}

.checkbox-label:hover {
  color: var(--primary-dark);
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 22px;
  height: 22px;
  border: 2px solid #cbd5e1;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
  margin-top: 2px;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: var(--accent-blue);
  border-color: var(--accent-blue);
  transform: scale(1.05);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: "✓";
  color: var(--pure-white);
  font-size: 14px;
  font-weight: bold;
}

/* Submit Button Enhancement */
.contact-form .btn-primary {
  width: 100%;
  padding: 1.2rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  margin-top: 1rem;
  background: linear-gradient(135deg, var(--accent-blue), #2563eb);
  border: none;
  border-radius: 12px;
  color: white;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.contact-form .btn-primary::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.contact-form .btn-primary:hover::before {
  left: 100%;
}

.contact-form .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

/* Contact Information */
.contact-info-container h2 {
  margin-bottom: 1rem;
  color: var(--primary-dark);
  font-size: 2rem;
  font-weight: 700;
}

.contact-info-container p {
  color: var(--secondary-gray);
  margin-bottom: 2.5rem;
  font-size: 1.1rem;
  line-height: 1.6;
  font-weight: 500;
}

.contact-methods {
  margin-bottom: 3rem;
}

.contact-method {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding: 2.5rem;
  background: var(--pure-white);
  border-radius: 16px;
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.contact-method::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, var(--accent-blue), var(--primary-dark));
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.contact-method:hover::before {
  transform: scaleY(1);
}

.contact-method:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.method-icon {
  font-size: 2.2rem;
  flex-shrink: 0;
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--accent-blue), var(--primary-dark));
  border-radius: 14px;
  color: var(--pure-white);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
  transition: all 0.3s ease;
}

.contact-method:hover .method-icon {
  background: linear-gradient(135deg, var(--accent-blue), #2563eb);
  transform: scale(1.05);
}

.method-content h3 {
  margin-bottom: 0.5rem;
  color: var(--primary-dark);
  font-size: 1.25rem;
  font-weight: 700;
  letter-spacing: -0.025em;
}

.method-content p {
  color: var(--secondary-gray);
  margin-bottom: 0.25rem;
  font-weight: 600;
  font-size: 1.05rem;
}

.method-content span {
  color: var(--text-light);
  font-size: 0.875rem;
  line-height: 1.4;
}

/* Social Contact */
.social-contact {
  background: var(--pure-white);
  padding: 2.5rem;
  border-radius: 16px;
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  margin-top: 2rem;
  position: relative;
  overflow: hidden;
}

.social-contact::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-blue), var(--primary-dark));
}

.social-contact h3 {
  margin-bottom: 1.5rem;
  color: var(--primary-dark);
  font-size: 1.3rem;
  font-weight: 600;
}

.social-contact .social-links {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.25rem;
}

.social-link {
  color: var(--text-light);
  text-decoration: none;
  padding: 1.25rem;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  border: 2px solid #e2e8f0;
  background: #fafbfc;
  font-weight: 600;
  font-size: 0.95rem;
  position: relative;
  overflow: hidden;
}

.social-link::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(59, 130, 246, 0.1),
    transparent
  );
  transition: left 0.5s;
}

.social-link:hover::before {
  left: 100%;
}

.social-link:hover {
  color: var(--accent-blue);
  background: var(--pure-white);
  border-color: var(--accent-blue);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
}

/* FAQ Section */
.faq-container {
  max-width: 900px;
  margin: 0 auto;
}

.faq-item {
  background: var(--pure-white);
  border-radius: 16px;
  margin-bottom: 2rem;
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.faq-item:hover {
  box-shadow: 0 10px 35px rgba(0, 0, 0, 0.12);
  transform: translateY(-3px);
}

.faq-question {
  padding: 2rem 2.5rem;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
  position: relative;
}

.faq-question::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(135deg, var(--accent-blue), var(--primary-dark));
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.faq-item.active .faq-question::before {
  transform: scaleY(1);
}

.faq-question:hover {
  background: #f8fafc;
}

.faq-question h3 {
  margin: 0;
  color: var(--primary-dark);
  font-size: 1.2rem;
  font-weight: 600;
  line-height: 1.4;
}

.faq-toggle {
  font-size: 1.8rem;
  color: var(--accent-blue);
  font-weight: bold;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f1f5f9;
}

.faq-item.active .faq-toggle {
  transform: rotate(45deg);
  background: var(--accent-blue);
  color: white;
}

.faq-answer {
  padding: 0 2.5rem;
  max-height: 0;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.faq-item.active .faq-answer {
  padding: 0 2.5rem 2rem;
  max-height: 300px;
}

.faq-answer p {
  color: var(--text-light);
  line-height: 1.7;
  margin: 0;
  font-size: 1rem;
}

/* Futuristic Enhancements */

/* Particle Effect Background */
.particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: var(--accent-cyan);
  border-radius: 50%;
  opacity: 0.6;
  animation: float 6s ease-in-out infinite;
}

.particle:nth-child(2n) {
  background: var(--accent-blue);
  animation-duration: 8s;
  animation-delay: -2s;
}

.particle:nth-child(3n) {
  background: var(--accent-purple);
  animation-duration: 10s;
  animation-delay: -4s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-100px) rotate(180deg);
    opacity: 1;
  }
}

/* Enhanced Glow Effects */
.glow-text {
  animation: textGlowPulse 2s ease-in-out infinite alternate;
}

@keyframes textGlowPulse {
  from {
    text-shadow: 0 0 10px var(--accent-cyan);
  }
  to {
    text-shadow: 0 0 20px var(--accent-cyan), 0 0 30px var(--accent-blue);
  }
}

/* Holographic Border Effect */
.holographic-border {
  position: relative;
  border: 1px solid transparent;
  background: linear-gradient(var(--bg-ultra-dark), var(--bg-ultra-dark))
      padding-box,
    var(--gradient-accent) border-box;
}

.holographic-border::before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: var(--gradient-accent);
  border-radius: inherit;
  z-index: -1;
  opacity: 0.7;
  filter: blur(1px);
}

/* Scan Line Effect */
.scan-lines::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: repeating-linear-gradient(
    0deg,
    transparent,
    transparent 2px,
    rgba(59, 240, 228, 0.03) 2px,
    rgba(59, 240, 228, 0.03) 4px
  );
  pointer-events: none;
}

/* Matrix Rain Effect for Hero */
.matrix-rain {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}

.matrix-column {
  position: absolute;
  top: -100%;
  width: 20px;
  height: 100%;
  background: linear-gradient(
    to bottom,
    transparent,
    var(--accent-cyan),
    transparent
  );
  opacity: 0.1;
  animation: matrixFall 3s linear infinite;
}

@keyframes matrixFall {
  to {
    transform: translateY(100vh);
  }
}

/* Enhanced Button Hover Effects */
.btn-futuristic {
  position: relative;
  overflow: hidden;
  background: var(--gradient-glass);
  border: var(--border-glow);
  backdrop-filter: blur(10px);
}

.btn-futuristic::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(59, 240, 228, 0.4),
    transparent
  );
  transition: left 0.5s ease;
}

.btn-futuristic:hover::before {
  left: 100%;
}

/* Responsive Design for Contact Page */
@media (max-width: 1024px) {
  .contact-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .contact-section {
    padding: 3rem 0;
  }
}

@media (max-width: 768px) {
  .contact-content {
    grid-template-columns: 1fr;
    gap: 2.5rem;
  }

  .contact-section {
    padding: 2rem 0;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .contact-form {
    padding: 2rem;
    border-radius: 12px;
  }

  .contact-form-container h2 {
    font-size: 1.8rem;
  }

  .contact-info-container h2 {
    font-size: 1.6rem;
  }

  .contact-method {
    padding: 2rem;
    gap: 1.25rem;
    margin-bottom: 1.5rem;
  }

  .method-icon {
    width: 56px;
    height: 56px;
    font-size: 2rem;
  }

  .social-contact {
    padding: 2rem;
  }

  .social-contact .social-links {
    grid-template-columns: 1fr;
  }

  .faq-question {
    padding: 1.5rem 2rem;
  }

  .faq-question h3 {
    font-size: 1.1rem;
  }

  .faq-item.active .faq-answer {
    padding: 0 2rem 1.5rem;
  }
}

@media (max-width: 480px) {
  .contact-section {
    padding: 1.5rem 0;
  }

  .contact-form {
    padding: 1.5rem;
  }

  .contact-form-container h2 {
    font-size: 1.5rem;
  }

  .contact-info-container h2 {
    font-size: 1.4rem;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    padding: 1rem;
    font-size: 0.95rem;
  }

  .form-group label {
    font-size: 0.9rem;
  }

  .contact-method {
    padding: 1rem;
    text-align: center;
    gap: 1rem;
  }

  .method-icon {
    width: 40px;
    height: 40px;
    font-size: 1.5rem;
    margin: 0 auto;
  }

  .method-content h3 {
    margin-bottom: 0.5rem;
    font-size: 1rem;
  }

  .method-content p {
    font-size: 0.9rem;
  }

  .social-contact {
    padding: 1.5rem;
  }

  .social-contact h3 {
    font-size: 1.1rem;
  }

  .faq-question {
    padding: 1rem 1.5rem;
  }

  .faq-question h3 {
    font-size: 1rem;
  }

  .faq-toggle {
    width: 28px;
    height: 28px;
    font-size: 1.5rem;
  }

  .faq-item.active .faq-answer {
    padding: 0 1.5rem 1rem;
  }

  .checkbox-group {
    padding: 1rem;
  }
}
